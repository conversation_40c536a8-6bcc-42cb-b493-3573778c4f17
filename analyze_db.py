#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sqlite3
import struct

def analyze_file_header(filepath):
    """分析文件头部信息"""
    try:
        with open(filepath, 'rb') as f:
            header = f.read(100)  # 读取前100字节
            
        print(f"\n=== 分析文件: {filepath} ===")
        print(f"文件大小: {os.path.getsize(filepath)} bytes")
        
        # 检查SQLite魔术字节
        if header.startswith(b'SQLite format 3\x00'):
            print("✓ 标准SQLite数据库文件")
            analyze_sqlite_header(header)
        else:
            print("✗ 非标准SQLite文件头")
            print("前16字节 (hex):", ' '.join(f'{b:02x}' for b in header[:16]))
            print("前16字节 (ascii):", ''.join(chr(b) if 32 <= b <= 126 else '.' for b in header[:16]))
            
            # 检查是否可能是加密的SQLite
            if is_likely_encrypted(header):
                print("⚠ 可能是加密的数据库文件")
            
        return header
        
    except Exception as e:
        print(f"读取文件失败: {e}")
        return None

def analyze_sqlite_header(header):
    """分析SQLite文件头部结构"""
    try:
        # SQLite文件头部结构分析
        page_size = struct.unpack('>H', header[16:18])[0]
        if page_size == 1:
            page_size = 65536
        print(f"页面大小: {page_size} bytes")
        
        write_version = header[18]
        read_version = header[19]
        print(f"写入版本: {write_version}, 读取版本: {read_version}")
        
        reserved_space = header[20]
        print(f"保留空间: {reserved_space}")
        
        # 检查文件变更计数器
        change_counter = struct.unpack('>I', header[24:28])[0]
        print(f"文件变更计数器: {change_counter}")
        
    except Exception as e:
        print(f"分析SQLite头部失败: {e}")

def is_likely_encrypted(header):
    """判断是否可能是加密文件"""
    # 检查熵值 - 加密文件通常有较高的熵值
    byte_counts = [0] * 256
    for byte in header[:64]:  # 检查前64字节
        byte_counts[byte] += 1
    
    # 计算简单的熵值指标
    non_zero_bytes = sum(1 for count in byte_counts if count > 0)
    
    # 如果字节分布比较均匀，可能是加密的
    if non_zero_bytes > 32:  # 前64字节中有超过32种不同字节
        return True
    
    # 检查是否有明显的模式
    if header[:4] == b'\x00\x00\x00\x00' or header[:4] == b'\xff\xff\xff\xff':
        return False
    
    return False

def try_open_sqlite(filepath):
    """尝试打开SQLite数据库"""
    try:
        conn = sqlite3.connect(filepath)
        cursor = conn.cursor()
        
        # 尝试获取表列表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print(f"✓ 成功打开数据库，找到 {len(tables)} 个表:")
        for table in tables:
            print(f"  - {table[0]}")
            
        conn.close()
        return True
        
    except sqlite3.DatabaseError as e:
        print(f"✗ 无法打开SQLite数据库: {e}")
        if "file is not a database" in str(e).lower():
            print("  可能原因: 文件已加密")
        elif "database disk image is malformed" in str(e).lower():
            print("  可能原因: 数据库损坏或使用了不兼容的加密")
        return False
    except Exception as e:
        print(f"✗ 打开数据库时发生错误: {e}")
        return False

def main():
    """主函数"""
    print("腾讯元宝数据库加密分析工具")
    print("=" * 50)
    
    # 获取所有.db文件
    db_files = [f for f in os.listdir('.') if f.endswith('.db')]
    
    print(f"找到 {len(db_files)} 个数据库文件:")
    for db_file in db_files:
        print(f"  - {db_file}")
    
    # 分析每个数据库文件
    encrypted_files = []
    normal_files = []
    
    for db_file in db_files:
        header = analyze_file_header(db_file)
        if header:
            if try_open_sqlite(db_file):
                normal_files.append(db_file)
            else:
                encrypted_files.append(db_file)
    
    # 总结
    print("\n" + "=" * 50)
    print("分析总结:")
    print(f"正常SQLite文件: {len(normal_files)}")
    for f in normal_files:
        print(f"  ✓ {f}")
    
    print(f"可能加密的文件: {len(encrypted_files)}")
    for f in encrypted_files:
        print(f"  ⚠ {f}")
    
    if encrypted_files:
        print("\n可能的加密方式:")
        print("1. SQLCipher - 常用的SQLite加密扩展")
        print("2. 自定义加密 - 腾讯可能使用自己的加密方案")
        print("3. 系统级加密 - Android系统提供的数据库加密")

if __name__ == "__main__":
    main()
