#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
import binascii

def analyze_wal_file(filepath):
    """分析WAL文件中的Java序列化数据"""
    try:
        with open(filepath, 'rb') as f:
            data = f.read()
        
        print(f"分析WAL文件: {filepath}")
        print(f"文件大小: {len(data)} bytes")
        print("=" * 50)
        
        # 查找Java序列化对象
        find_java_objects(data)
        
        # 提取统计数据
        extract_statistics_data(data)
        
        # 查找设备信息
        find_device_info(data)
        
        # 分析事件数据
        analyze_event_data(data)
        
    except Exception as e:
        print(f"分析失败: {e}")

def find_java_objects(data):
    """查找Java序列化对象"""
    print("🔍 Java序列化对象分析:")
    
    # 查找类名
    class_patterns = [
        rb'com\.tencent\.beacon\.event\.EventBean',
        rb'java\.util\.HashMap',
        rb'java\.lang\.String'
    ]
    
    for pattern in class_patterns:
        matches = re.findall(pattern, data)
        if matches:
            print(f"  ✓ 发现 {len(matches)} 个 {pattern.decode('utf-8', errors='ignore')} 对象")
    
    # 查找序列化标识
    if b'\xac\xed\x00\x05' in data:
        print("  ✓ 包含标准Java序列化数据")
    
    print()

def extract_statistics_data(data):
    """提取统计数据"""
    print("📊 统计数据分析:")
    
    # 查找常见的统计字段
    stat_fields = [
        b'eventTime', b'eventType', b'eventResult',
        b'appKey', b'device_id', b'log_unique_id',
        b'wifi', b'release', b'Android'
    ]
    
    found_fields = []
    for field in stat_fields:
        if field in data:
            found_fields.append(field.decode('utf-8', errors='ignore'))
    
    if found_fields:
        print(f"  ✓ 发现统计字段: {', '.join(found_fields)}")
    
    # 查找时间戳
    timestamp_pattern = rb'\d{13}'  # 13位时间戳
    timestamps = re.findall(timestamp_pattern, data)
    if timestamps:
        print(f"  ✓ 发现 {len(timestamps)} 个时间戳")
        # 显示前几个时间戳
        for i, ts in enumerate(timestamps[:3]):
            try:
                ts_str = ts.decode('utf-8')
                print(f"    - {ts_str}")
            except:
                pass
    
    print()

def find_device_info(data):
    """查找设备信息"""
    print("📱 设备信息分析:")
    
    # 查找设备相关信息
    device_patterns = [
        (rb'[A-Z0-9]{16}', '设备ID'),  # 16位设备ID
        (rb'Android', 'Android系统'),
        (rb'wifi', 'WiFi连接'),
        (rb'release/[\d\.]+', '版本信息'),
        (rb'\d+\.\d+\.\d+\.\d+', 'IP地址')
    ]
    
    for pattern, desc in device_patterns:
        matches = re.findall(pattern, data)
        if matches:
            print(f"  ✓ {desc}: 发现 {len(matches)} 个匹配")
            # 显示前几个匹配项
            for match in matches[:3]:
                try:
                    print(f"    - {match.decode('utf-8', errors='ignore')}")
                except:
                    pass
    
    print()

def analyze_event_data(data):
    """分析事件数据"""
    print("🎯 事件数据分析:")
    
    # 查找事件相关数据
    event_patterns = [
        (rb'HY\d+', '华为事件代码'),
        (rb'A\d+', 'A类事件代码'),
        (rb'OnAgent\w+', '代理事件'),
        (rb'dtmp_\w+', '临时数据事件')
    ]
    
    for pattern, desc in event_patterns:
        matches = re.findall(pattern, data)
        if matches:
            print(f"  ✓ {desc}: 发现 {len(matches)} 个")
            # 显示前几个匹配项
            unique_matches = list(set(matches))[:5]
            for match in unique_matches:
                try:
                    print(f"    - {match.decode('utf-8', errors='ignore')}")
                except:
                    pass
    
    print()

def extract_readable_strings(data, min_length=4):
    """提取可读字符串"""
    print("📝 可读字符串提取:")
    
    # 查找可打印字符串
    strings = re.findall(rb'[a-zA-Z0-9_\-\.]{' + str(min_length).encode() + rb',}', data)
    
    # 过滤和去重
    unique_strings = list(set(strings))
    interesting_strings = []
    
    for s in unique_strings:
        try:
            decoded = s.decode('utf-8')
            # 过滤有意义的字符串
            if any(keyword in decoded.lower() for keyword in 
                   ['hunyuan', 'tencent', 'beacon', 'android', 'event', 'wifi', 'release']):
                interesting_strings.append(decoded)
        except:
            pass
    
    if interesting_strings:
        print(f"  ✓ 发现 {len(interesting_strings)} 个有意义的字符串:")
        for s in sorted(interesting_strings)[:10]:  # 显示前10个
            print(f"    - {s}")
    
    print()

def main():
    """主函数"""
    wal_file = "beacon_db_com.tencent.hunyuan.app.chat-wal"
    
    print("腾讯元宝WAL文件详细分析")
    print("=" * 60)
    
    analyze_wal_file(wal_file)
    
    print("=" * 60)
    print("📋 分析总结:")
    print("1. WAL文件包含大量Java序列化的统计数据")
    print("2. 数据包含设备信息、应用使用统计、网络状态等")
    print("3. 虽然不是传统加密，但序列化格式增加了数据访问复杂性")
    print("4. 可以通过Java反序列化工具进一步分析具体内容")
    print("5. 主要用于应用行为分析和统计上报")

if __name__ == "__main__":
    main()
