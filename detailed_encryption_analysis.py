#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import struct
import binascii

def analyze_file_encryption(filepath):
    """详细分析文件的加密状态"""
    try:
        with open(filepath, 'rb') as f:
            data = f.read(1024)  # 读取前1KB数据
            
        print(f"\n=== 分析文件: {filepath} ===")
        print(f"文件大小: {os.path.getsize(filepath)} bytes")
        
        # 检查文件头
        if len(data) >= 16:
            header_hex = binascii.hexlify(data[:16]).decode('ascii')
            header_ascii = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in data[:16])
            
            print(f"前16字节 (hex): {' '.join(header_hex[i:i+2] for i in range(0, len(header_hex), 2))}")
            print(f"前16字节 (ascii): {header_ascii}")
            
            # 检查SQLite魔术字节
            if data.startswith(b'SQLite format 3\x00'):
                print("✅ 标准SQLite数据库文件")
                analyze_sqlite_details(data)
                return "unencrypted_sqlite"
            
            # 检查WAL文件头
            elif data.startswith(b'\x37\x7f\x06\x82') or data.startswith(b'\x37\x7f\x06\x83'):
                print("📝 SQLite WAL (Write-Ahead Log) 文件")
                analyze_wal_file(data, filepath)
                return "wal_file"
            
            # 检查是否包含序列化的Java对象
            elif b'sr ' in data[:100] and b'com.tencent' in data[:200]:
                print("☕ 包含序列化的Java对象数据")
                analyze_java_serialization(data)
                return "java_serialized"
            
            # 检查熵值判断是否加密
            else:
                entropy = calculate_entropy(data[:256])
                print(f"数据熵值: {entropy:.3f}")
                
                if entropy > 7.5:
                    print("🔒 可能是加密数据 (高熵值)")
                    return "possibly_encrypted"
                elif entropy < 4.0:
                    print("📄 可能是结构化数据 (低熵值)")
                    return "structured_data"
                else:
                    print("❓ 数据格式未知")
                    return "unknown"
        
    except Exception as e:
        print(f"❌ 分析文件失败: {e}")
        return "error"

def analyze_sqlite_details(data):
    """分析SQLite文件详细信息"""
    try:
        if len(data) >= 100:
            page_size = struct.unpack('>H', data[16:18])[0]
            if page_size == 1:
                page_size = 65536
            print(f"   页面大小: {page_size} bytes")
            
            write_version = data[18]
            read_version = data[19]
            print(f"   文件格式版本: 写入={write_version}, 读取={read_version}")
            
            # 检查是否有加密标识
            if data[20] != 0:  # 保留字节通常为0
                print(f"   ⚠️ 保留字节非零: {data[20]} (可能表示加密)")
    except:
        pass

def analyze_wal_file(data, filepath):
    """分析WAL文件"""
    try:
        # WAL文件头部分析
        if len(data) >= 32:
            magic = struct.unpack('>I', data[0:4])[0]
            file_format = struct.unpack('>I', data[4:8])[0]
            page_size = struct.unpack('>I', data[8:12])[0]
            
            print(f"   WAL魔术数: 0x{magic:08x}")
            print(f"   文件格式: {file_format}")
            print(f"   页面大小: {page_size}")
            
            # 检查是否包含加密数据
            if b'com.tencent.beacon' in data:
                print("   📊 包含腾讯Beacon统计数据")
            
            # 检查Java序列化标识
            if b'\xac\xed' in data:
                print("   ☕ 包含Java序列化数据")
                
    except:
        pass

def analyze_java_serialization(data):
    """分析Java序列化数据"""
    try:
        # 查找Java序列化标识
        if b'\xac\xed\x00\x05' in data:
            print("   ☕ 标准Java序列化格式")
        
        # 查找类名
        if b'com.tencent.beacon.event.EventBean' in data:
            print("   📊 包含EventBean事件数据")
        
        if b'java.util.HashMap' in data:
            print("   🗂️ 包含HashMap数据结构")
            
        # 检查是否有加密的blob数据
        blob_count = data.count(b'blob')
        if blob_count > 0:
            print(f"   💾 发现 {blob_count} 个blob字段")
            
    except:
        pass

def calculate_entropy(data):
    """计算数据熵值"""
    if not data:
        return 0
    
    # 统计字节频率
    byte_counts = [0] * 256
    for byte in data:
        byte_counts[byte] += 1
    
    # 计算熵值
    entropy = 0
    data_len = len(data)
    for count in byte_counts:
        if count > 0:
            probability = count / data_len
            entropy -= probability * (probability.bit_length() - 1)
    
    return entropy

def main():
    """主函数"""
    print("腾讯元宝数据库详细加密分析")
    print("=" * 60)
    
    # 获取所有数据库相关文件
    db_files = []
    for file in os.listdir('.'):
        if (file.endswith('.db') or 
            file.endswith('-wal') or 
            file.endswith('-shm') or 
            file.endswith('-journal')):
            db_files.append(file)
    
    print(f"找到 {len(db_files)} 个数据库相关文件")
    
    # 分类统计
    results = {
        'unencrypted_sqlite': [],
        'wal_file': [],
        'java_serialized': [],
        'possibly_encrypted': [],
        'structured_data': [],
        'unknown': [],
        'error': []
    }
    
    # 分析每个文件
    for db_file in sorted(db_files):
        result = analyze_file_encryption(db_file)
        results[result].append(db_file)
    
    # 输出总结
    print("\n" + "=" * 60)
    print("📊 分析总结:")
    
    for category, files in results.items():
        if files:
            category_names = {
                'unencrypted_sqlite': '✅ 未加密SQLite文件',
                'wal_file': '📝 WAL日志文件',
                'java_serialized': '☕ Java序列化数据',
                'possibly_encrypted': '🔒 可能加密的文件',
                'structured_data': '📄 结构化数据文件',
                'unknown': '❓ 未知格式文件',
                'error': '❌ 分析失败文件'
            }
            
            print(f"\n{category_names.get(category, category)} ({len(files)}个):")
            for file in files:
                print(f"  • {file}")
    
    # 加密分析结论
    print("\n" + "=" * 60)
    print("🔍 加密分析结论:")
    
    if results['possibly_encrypted']:
        print("⚠️  发现可能加密的文件，需要进一步分析")
    
    if results['java_serialized'] or results['wal_file']:
        print("📊 WAL文件包含序列化的统计数据，数据本身未加密但格式复杂")
    
    if results['unencrypted_sqlite']:
        print("✅ 主要数据库文件未加密，可直接访问")
    
    print("\n💡 建议:")
    print("1. WAL文件包含应用运行时的统计数据")
    print("2. 可以使用Java反序列化工具分析WAL文件中的对象数据")
    print("3. 主数据库文件可直接用SQLite工具打开")

if __name__ == "__main__":
    main()
