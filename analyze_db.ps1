# 腾讯元宝数据库加密分析脚本
Write-Host "腾讯元宝数据库加密分析工具" -ForegroundColor Green
Write-Host "=" * 50

# 获取所有.db文件
$dbFiles = Get-ChildItem -Path "." -Filter "*.db" | Select-Object -ExpandProperty Name

Write-Host "找到 $($dbFiles.Count) 个数据库文件:" -ForegroundColor Yellow
foreach ($file in $dbFiles) {
    Write-Host "  - $file"
}

Write-Host ""

# 分析每个数据库文件
$encryptedFiles = @()
$normalFiles = @()

foreach ($dbFile in $dbFiles) {
    Write-Host "=== 分析文件: $dbFile ===" -ForegroundColor Cyan
    
    try {
        # 获取文件大小
        $fileSize = (Get-Item $dbFile).Length
        Write-Host "文件大小: $fileSize bytes"
        
        # 读取文件头部
        $fileStream = [System.IO.File]::OpenRead($dbFile)
        $header = New-Object byte[] 100
        $bytesRead = $fileStream.Read($header, 0, 100)
        $fileStream.Close()
        
        # 检查SQLite魔术字节
        $sqliteHeader = [System.Text.Encoding]::ASCII.GetBytes("SQLite format 3")
        $isSQLite = $true
        
        for ($i = 0; $i -lt $sqliteHeader.Length; $i++) {
            if ($header[$i] -ne $sqliteHeader[$i]) {
                $isSQLite = $false
                break
            }
        }
        
        if ($isSQLite) {
            Write-Host "✓ 标准SQLite数据库文件" -ForegroundColor Green
            
            # 分析SQLite头部
            $pageSize = [BitConverter]::ToUInt16($header[16..17], 0)
            if ([BitConverter]::IsLittleEndian) {
                $pageSize = [BitConverter]::ToUInt16([array]::Reverse($header[16..17]), 0)
            }
            if ($pageSize -eq 1) { $pageSize = 65536 }
            Write-Host "页面大小: $pageSize bytes"
            
            $writeVersion = $header[18]
            $readVersion = $header[19]
            Write-Host "写入版本: $writeVersion, 读取版本: $readVersion"
            
            $normalFiles += $dbFile
        }
        else {
            Write-Host "✗ 非标准SQLite文件头" -ForegroundColor Red
            
            # 显示前16字节
            $hexString = ""
            $asciiString = ""
            for ($i = 0; $i -lt 16; $i++) {
                $hexString += "{0:X2} " -f $header[$i]
                if ($header[$i] -ge 32 -and $header[$i] -le 126) {
                    $asciiString += [char]$header[$i]
                } else {
                    $asciiString += "."
                }
            }
            Write-Host "前16字节 (hex): $hexString"
            Write-Host "前16字节 (ascii): $asciiString"
            
            # 检查是否可能加密
            $uniqueBytes = ($header[0..63] | Sort-Object -Unique).Count
            if ($uniqueBytes -gt 32) {
                Write-Host "⚠ 可能是加密的数据库文件" -ForegroundColor Yellow
                $encryptedFiles += $dbFile
            } else {
                Write-Host "? 文件格式未知" -ForegroundColor Magenta
                $encryptedFiles += $dbFile
            }
        }
        
        Write-Host ""
        
    }
    catch {
        Write-Host "读取文件失败: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host ""
    }
}

# 总结
Write-Host "=" * 50
Write-Host "分析总结:" -ForegroundColor Green
Write-Host "正常SQLite文件: $($normalFiles.Count)" -ForegroundColor Green
foreach ($file in $normalFiles) {
    Write-Host "  ✓ $file" -ForegroundColor Green
}

Write-Host "可能加密的文件: $($encryptedFiles.Count)" -ForegroundColor Yellow
foreach ($file in $encryptedFiles) {
    Write-Host "  ⚠ $file" -ForegroundColor Yellow
}

if ($encryptedFiles.Count -gt 0) {
    Write-Host ""
    Write-Host "可能的加密方式:" -ForegroundColor Cyan
    Write-Host "1. SQLCipher - 常用的SQLite加密扩展"
    Write-Host "2. 自定义加密 - 腾讯可能使用自己的加密方案"
    Write-Host "3. 系统级加密 - Android系统提供的数据库加密"
    Write-Host "4. WCDB - 微信开源的数据库框架，支持加密"
}

Write-Host ""
Write-Host "建议进一步分析步骤:" -ForegroundColor Magenta
Write-Host "1. 使用SQLite工具尝试打开正常文件"
Write-Host "2. 分析APK文件中的native库，查找加密相关代码"
Write-Host "3. 搜索常见的数据库密钥存储位置"
Write-Host "4. 使用逆向工程工具分析应用的数据库访问代码"
